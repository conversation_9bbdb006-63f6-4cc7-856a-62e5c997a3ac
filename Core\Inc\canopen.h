#ifndef __CANOPEN_H
#define __CANOPEN_H

#include "can.h"
#include "mt6825.h"  // 包含角度转换函数


extern CAN_TxHeaderTypeDef     TxMeg;
extern CAN_RxHeaderTypeDef     RxMeg;

extern uint8_t  CAN_Rx_Data[8];
extern uint8_t  CAN_Tx_Data[8];

extern uint8_t TPDO2_Data[8];		// 发送过程数据对象2缓冲区（电压温度电流）
extern uint8_t RPDO1_Data[8];		// 接收过程数据对象1缓冲区
extern uint8_t RPDO2_Data[8];		// 接收过程数据对象2缓冲区
extern uint8_t RPDO3_Data[8];		// 接收过程数据对象3缓冲区

extern uint16_t CAN_Baudrate;
extern uint8_t Error_register;
extern uint8_t Node_ID;              // 节点ID

extern uint16_t TPDO_Period,tim_count;

void CAN_User_Init(CAN_HandleTypeDef* hcan );
void CAN_TRANSMIT(void);
uint8_t CANx_SendNormalData(CAN_HandleTypeDef* hcan,uint16_t ID,uint8_t *pData,uint16_t Len);
void Init_PDO_Mapping(void);
void Process_TPDO(void);
void Process_TPDO2(void);  // 发送电压温度电流信息
void Process_RPDO(CAN_RxHeaderTypeDef *pHeader, uint8_t aData[]);
uint8_t SDO_Process(CAN_RxHeaderTypeDef *pHeader, uint8_t pbuf[]);
void Enter_Bootloader_Mode(void);  // 进入bootloader模式函数
void Enter_Bootloader_Mode_Alt(void);  // 进入bootloader模式函数（备用方案）

// 新增的SDO相关函数
void Send_SDO_Response(uint8_t node_id, uint8_t cmd, uint16_t index, uint8_t subindex, uint32_t data, uint8_t data_len);

// 辅助函数
uint32_t Get_Device_Status(void);
uint8_t Set_Node_ID(uint8_t new_node_id);

#endif
