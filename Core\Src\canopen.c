#include "can.h"
#include "mcpwm.h"
#include "vofa_function.h"
#include "Dic.h"
#include "canopen.h"
#include "string.h"
#include "core_cm3.h"  // 包含系统控制块定义

// 基础ID定义
// PDO消息ID
#define RPDO1_BASE_ID   0x200   // 接收PDO1基础ID
#define RPDO2_BASE_ID   0x300   // 接收PDO2基础ID  
#define RPDO3_BASE_ID   0x400   // 接收PDO3基础ID
#define TPDO1_BASE_ID   0x180   // 发送PDO1基础ID
#define TPDO2_BASE_ID   0x280   // 发送PDO2基础ID

// SDO消息ID
#define SDO_REQ_BASE_ID 0x600   // SDO请求基础ID
#define SDO_RSP_BASE_ID 0x580   // SDO响应基础ID

// SDO命令码定义
// 写命令
#define SDO_CMD_WRITE_1BYTE  0x2F   // 写1字节数据
#define SDO_CMD_WRITE_2BYTE  0x2B   // 写2字节数据
#define SDO_CMD_WRITE_4BYTE  0x23   // 写4字节数据
#define SDO_CMD_READ         0x40   // 读取请求

// 响应码
#define SDO_RSP_WRITE_OK     0x60   // 写操作成功
#define SDO_RSP_READ_1BYTE   0x4F   // 读取1字节响应
#define SDO_RSP_READ_2BYTE   0x4B   // 读取2字节响应
#define SDO_RSP_READ_4BYTE   0x43   // 读取4字节响应
#define SDO_RSP_ABORT        0x80   // 操作中止

// 对象字典索引
#define OD_BOOT_COMMAND 0x2001      // 启动命令对象
#define OD_BOOT_STATUS  0x2002      // 启动状态对象

// 启动命令码
#define CMD_JUMP_APP1    0x11       // 跳转APP1
#define CMD_JUMP_APP2    0x12       // 跳转APP2
#define CMD_SOFT_RESET   0xFF       // 软复位

// Bootloader标志位 - 使用固定地址确保bootloader和应用程序都能访问
uint32_t bootloader_flag __attribute__((at(0x20004FF0), zero_init));

CAN_TxHeaderTypeDef TxMessage1; // CAN发送消息结构体

// 2个3位标识符FIFO
#define CAN1FIFO CAN_RX_FIFO0 // CAN1接收FIFO
#define CAN2FIFO CAN_RX_FIFO1 // CAN2接收FIFO

CAN_TxHeaderTypeDef TxMeg; // CAN发送消息头定义
CAN_RxHeaderTypeDef RxMeg; // CAN接收消息头定义

uint8_t CAN_Rx_Data[8]; // CAN接收数据缓冲区
uint8_t CAN_Tx_Data[8]; // CAN发送数据缓冲区

uint8_t TPDO1_Data[8];		// 发送过程数据对象1缓冲区
uint8_t TPDO2_Data[8];		// 发送过程数据对象2缓冲区（电压温度电流）
uint8_t RPDO1_Data[8];		// 接收过程数据对象1缓冲区
uint8_t RPDO2_Data[8];		// 接收过程数据对象2缓冲区
uint8_t RPDO3_Data[8];		// 接收过程数据对象3缓冲区

uint16_t CAN_Baudrate = 7;	// CAN波特率设置，默认为7（1000kbit/s）
uint8_t Error_register = 0; // 错误寄存器
uint8_t Node_ID = 1;        // 节点ID，可配置

uint16_t TPDO_Period = 100, tim_count = 0; // TPDO发送周期，1表示0.1ms

// SDO相关变量
uint32_t SDO_Error_Code = 0;  // SDO错误码
uint8_t SDO_Response_Pending = 0;  // SDO响应等待标志

void CAN_User_Init(CAN_HandleTypeDef *hcan) // CAN用户初始化函数
{
	CAN_FilterTypeDef sFilterConfig; // CAN过滤器配置结构体
	HAL_StatusTypeDef HAL_Status;	 // HAL状态变量

	TxMeg.IDE = CAN_ID_STD;	  // 使用标准帧ID
	TxMeg.RTR = CAN_RTR_DATA; // 数据帧

	switch (CAN_Baudrate) // 根据波特率设置预分频值
	{
	case 0:
		hcan->Init.Prescaler = 100; // 20kbit/s
		break;
	case 1:
		hcan->Init.Prescaler = 40; // 50kbit/s
		break;
	case 2:
		hcan->Init.Prescaler = 20; // 100kbit/s
		break;
	case 3:
		hcan->Init.Prescaler = 16; // 125kbit/s
		break;
	case 4:
		hcan->Init.Prescaler = 8; // 250kbit/s
		break;
	case 5:
		hcan->Init.Prescaler = 4; // 500kbit/s
		break;
	case 7:
		hcan->Init.Prescaler = 2; // 1000kbit/s
		break;
	default:
		hcan->Init.Prescaler = 8; // 默认250kbit/s
		break;
	}

	if (HAL_CAN_Init(hcan) != HAL_OK) // 初始化CAN
	{
		Error_Handler(); // 如果初始化失败，调用错误处理函数
	}

	sFilterConfig.FilterBank = 0;					   // 使用过滤器0
	sFilterConfig.FilterMode = CAN_FILTERMODE_IDMASK;  // 设置为ID掩码模式
	sFilterConfig.FilterScale = CAN_FILTERSCALE_32BIT; // 32位过滤器

	sFilterConfig.FilterIdHigh = 0X0000; // 过滤器ID高16位
	sFilterConfig.FilterIdLow = 0X0000;	 // 过滤器ID低16位

	sFilterConfig.FilterMaskIdHigh = 0X0000;		   // 过滤器掩码高16位（0表示接收所有）
	sFilterConfig.FilterMaskIdLow = 0X0000;			   // 过滤器掩码低16位
	sFilterConfig.FilterFIFOAssignment = CAN_RX_FIFO0; // 过滤器关联到FIFO0

	sFilterConfig.FilterActivation = ENABLE; // 激活过滤器
	sFilterConfig.SlaveStartFilterBank = 0;	 // 从模式起始过滤器编号

	HAL_Status = HAL_CAN_ConfigFilter(hcan, &sFilterConfig); // 配置CAN过滤器
	if (HAL_Status != HAL_OK) Error_Handler();
	
	HAL_Status = HAL_CAN_Start(hcan);						 // 启动CAN
	if (HAL_Status != HAL_OK) Error_Handler();

	HAL_Status = HAL_CAN_ActivateNotification(hcan, CAN_IT_RX_FIFO0_MSG_PENDING); // 激活FIFO0接收中断
	if (HAL_Status != HAL_OK) Error_Handler();
}

void HAL_CAN_RxFifo0MsgPendingCallback(CAN_HandleTypeDef *hcan) // CAN接收中断回调函数
{
	HAL_StatusTypeDef HAL_RetVal;

	HAL_RetVal = HAL_CAN_GetRxMessage(hcan, CAN1FIFO, &RxMeg, CAN_Rx_Data); // 获取接收到的消息
	if (HAL_RetVal == HAL_OK)												// 如果接收成功
	{
		if (RxMeg.StdId == (SDO_REQ_BASE_ID + Node_ID) || RxMeg.StdId == (0x600 + RS485_Addr)) // SDO请求
			SDO_Process(&RxMeg, CAN_Rx_Data);	 // 处理SDO请求（使用原有函数）
		else
			Process_RPDO(&RxMeg, CAN_Rx_Data);		
	}
}

void Process_TPDO(void) // 发送PDO
{	
	static int16_t angle_deg = 0;
	uint32_t TxMailbox = 0;				   // 发送邮箱编号
	TxMessage1.DLC = 8;					   // 数据长度为8字节
	TxMessage1.StdId = TPDO1_BASE_ID + Node_ID; // TPDO1的ID
	TxMessage1.IDE = CAN_ID_STD;		   // 标准帧
	TxMessage1.RTR = CAN_RTR_DATA;		   // 数据帧

	angle_deg = encoder_to_angle_degree(pos_actual);
	// printf("pos_actual=%d\n",pos_actual);
	// printf("angle_deg=%d\n",angle_deg);

	TPDO1_Data[0] = (real_speed_filter) & 0xff;		  // 小端序：低字节在前
	TPDO1_Data[1] = (real_speed_filter >> 8) & 0xff;  
	TPDO1_Data[2] = (real_speed_filter >> 16) & 0xff; 
	TPDO1_Data[3] = (real_speed_filter >> 24) & 0xff; 

	TPDO1_Data[4] = (angle_deg) & 0xff;       // 小端序：低字节在前
	TPDO1_Data[5] = (angle_deg >> 8) & 0xff;  
	
	TPDO1_Data[6] = (Iq_real) & 0xff;       // 小端序：低字节在前
	TPDO1_Data[7] = (Iq_real >> 8) & 0xff;       

	if (HAL_CAN_AddTxMessage(&hcan, &TxMessage1, TPDO1_Data, &TxMailbox) != HAL_OK)
	{
		printf("TPDO1 send failed\n");
		Error_register |= 0x10;  // 设置通信错误标志
	}
}

void Process_TPDO2(void) // 发送电压温度电流信息
{
	uint32_t TxMailbox = 0;				   // 发送邮箱编号
	TxMessage1.DLC = 8;					   // 数据长度为8字节
	TxMessage1.StdId = TPDO2_BASE_ID + Node_ID; // TPDO2的ID为基础ID+节点ID
	TxMessage1.IDE = CAN_ID_STD;		   // 标准帧
	TxMessage1.RTR = CAN_RTR_DATA;		   // 数据帧

	// 将电压、温度、电流信息打包到TPDO2数据中（小端序）
	TPDO2_Data[0] = (vbus_voltage) & 0xff;       // 总线电压低字节
	TPDO2_Data[1] = (vbus_voltage >> 8) & 0xff;  // 总线电压高字节
	
	TPDO2_Data[2] = (device_temperature) & 0xff;       // 设备温度低字节
	TPDO2_Data[3] = (device_temperature >> 8) & 0xff;  // 设备温度高字节

	     
	if (HAL_CAN_AddTxMessage(&hcan, &TxMessage1, TPDO2_Data, &TxMailbox) != HAL_OK)
	{
		printf("TPDO2 send failed\n");
		Error_register |= 0x10;  // 设置通信错误标志
	}
}

void Process_RPDO(CAN_RxHeaderTypeDef *pHeader, uint8_t aData[]) // 处理接收PDO
{
	u16 rpdonum, rpdoid;
	rpdonum = pHeader->StdId & 0x780; // 获取功能码（高4位）
	rpdoid = pHeader->StdId & 0x07F;  // 获取节点ID（低7位）
	// 打印接收到的CAN帧信息              
	// printf("RPDO Received - ID: 0x%03X, DLC: %d, Data: ", pHeader->StdId, pHeader->DLC);
	// for (int i = 0; i < pHeader->DLC; i++)
	// {
	// 	printf("%02X ", aData[i]);
	// }
	// printf("| Type: 0x%03X, NodeID: %d\n", rpdonum, rpdoid);
	if (rpdoid == RS485_Addr) // 如果是发给当前节点的
	{
		switch (rpdonum) // 根据RPDO类型处理
		{
		case 0x200:
			target_Id = aData[4] + (aData[5] << 8) + (aData[6] << 16) + (aData[7] << 24);
			printf("current - Target: %d\n", target_Id);
			break;
		case 0x300:
			target_speed = aData[4] + (aData[5] << 8) + (aData[6] << 16) + (aData[7] << 24);
			printf("speed - Target: %d\n", target_speed);
			break;
		default:
			printf("Unknown RPDO type: 0x%03X\n", rpdonum);
			break;
		}
	}
}

DICT_OBJECT SDO_Access_OBJ;									 // SDO访问对象
u8 SDO_Process(CAN_RxHeaderTypeDef *pHeader, uint8_t pbuf[]) // SDO处理函数
{
	u8 state = 0;						   // 处理状态
	uint32_t TxMailbox = 0;				   // 发送邮箱编号
										   // int i=0;
	TxMessage1.DLC = 8;					   // 数据长度为8字节
	TxMessage1.StdId = SDO_RSP_BASE_ID + Node_ID; // SDO响应ID为基础ID+节点ID
	TxMessage1.IDE = CAN_ID_STD;		   // 标准帧
	TxMessage1.RTR = CAN_RTR_DATA;		   // 数据帧

	HAL_GPIO_TogglePin(LED_2_GPIO_Port, LED_2_Pin); // 翻转LED2状态，指示SDO处理

	uint16_t index = pbuf[1] + (pbuf[2] << 8);
	uint8_t subindex = pbuf[3];
	uint32_t data;

	if (pbuf[0] == SDO_CMD_WRITE_1BYTE) {
		data = pbuf[4];  // 1字节数据在第4个位置
	} else if (pbuf[0] == SDO_CMD_WRITE_2BYTE) {
		data = pbuf[4] | (pbuf[5] << 8);  // 2字节小端序
	} else if (pbuf[0] == SDO_CMD_WRITE_4BYTE) {
		data = pbuf[4] | (pbuf[5] << 8) | (pbuf[6] << 16) | (pbuf[7] << 24);  // 4字节小端序
	} else {
		data = pbuf[4] | (pbuf[5] << 8) | (pbuf[6] << 16) | (pbuf[7] << 24);  // 统一小端序格式
	}

	switch (pbuf[0]) // 根据SDO命令字处理
	{
	case SDO_CMD_WRITE_1BYTE:  // 0x2F - 写1字节
	case SDO_CMD_WRITE_2BYTE:  // 0x2B - 写2字节  
	case SDO_CMD_WRITE_4BYTE:  // 0x23 - 写4字节
	case SDO_W:			  // SDO写命令（兼容旧的）
        
        // 处理启动命令 (OD_BOOT_COMMAND)
        if(index == OD_BOOT_COMMAND && subindex == 0)
        {
            uint32_t jump_flag = 0; // 用于存储将要设置的跳转标志
            const char* log_message = NULL; // 用于存储日志消息

            // 1. 根据命令，确定跳转标志和日志消息
            switch(data)
            {
                case CMD_JUMP_APP1:
                    jump_flag = 0x12345678; // APP1标志
                    log_message = "Jump to APP1 command received";
                    break;
                    
                case CMD_JUMP_APP2:
                    jump_flag = 0x87654321; // APP2标志
                    log_message = "Jump to APP2 command received";
                    break;
                    
                case CMD_SOFT_RESET:
                    jump_flag = 0x20004F00; // Bootloader标志
                    log_message = "Jump to bootloader command received";
                    break;
            }

            // 2. 根据是否是有效命令，执行不同操作
            if (log_message != NULL) // 如果是有效命令
            {
                // 准备成功响应
                CAN_Tx_Data[0] = SDO_RSP_WRITE_OK;  // 0x60
                memcpy(&CAN_Tx_Data[1], &pbuf[1], 7);
                printf("%s\n", log_message);
                
                // 发送响应后执行跳转
                if (HAL_CAN_AddTxMessage(&hcan, &TxMessage1, CAN_Tx_Data, &TxMailbox) == HAL_OK)
                {
                    HAL_Delay(10);  // 等待发送完成
                    bootloader_flag = jump_flag; // 设置对应的跳转标志
                    NVIC_SystemReset(); // 执行复位
                }
                return 1; 
            }
            else // 如果是无效命令
            {
                // 准备中止响应
                CAN_Tx_Data[0] = SDO_RSP_ABORT;  // 0x80
                memcpy(&CAN_Tx_Data[1], &pbuf[1], 3); // 复制索引和子索引
                // 设置错误码 0x06010000 - 无效值 (小端)
                CAN_Tx_Data[4] = 0x00;
                CAN_Tx_Data[5] = 0x00;
                CAN_Tx_Data[6] = 0x01;
                CAN_Tx_Data[7] = 0x06;
                printf("Invalid boot command: 0x%lX\n", data);
                
                // 发送错误响应
                HAL_CAN_AddTxMessage(&hcan, &TxMessage1, CAN_Tx_Data, &TxMailbox);
                return 1;
            }
        }
     
        // 原有的其他处理逻辑
        else if (Write_Access) // 如果允许写操作
		{
			SDO_Access_OBJ.Index = pbuf[1] + (pbuf[2] << 8); // 获取索引（小端序）
			SDO_Access_OBJ.SubIndex = pbuf[3];				  // 获取子索引
			SDO_Access_OBJ.OD_pointer = &pbuf[4];			  // 数据指针指向SDO数据区
			state = Write_OD(SDO_Access_OBJ);				  // 写对象字典

			memcpy(&CAN_Tx_Data[1], &pbuf[1], 7); // 复制索引等信息到响应数据中
			if (state == 1)						  // 写入成功
			{
				CAN_Tx_Data[0] = SDO_RSP_WRITE_OK; // 0x60
			}
			else // 写入失败
			{
				CAN_Tx_Data[0] = SDO_RSP_ABORT; // 0x80
			}
		}
		else // 如果不允许通用写操作，仅允许部分特定参数写入
		{
			SDO_Access_OBJ.Index = pbuf[1] + (pbuf[2] << 8); // 获取索引（小端序）
			SDO_Access_OBJ.SubIndex = pbuf[3];				  // 获取子索引
			SDO_Access_OBJ.OD_pointer = &pbuf[4];			  // 数据指针
			switch (SDO_Access_OBJ.Index)
			{
			case 0x8888: // 特殊参数允许写入
				Write_OD(SDO_Access_OBJ);
				CAN_Tx_Data[0] = SDO_RSP_WRITE_OK; // 0x60
				memcpy(&CAN_Tx_Data[1], &pbuf[1], 7);
				break;
			default:
				// 不允许写入
				CAN_Tx_Data[0] = SDO_RSP_ABORT; // 0x80
				memcpy(&CAN_Tx_Data[1], &pbuf[1], 7);
				break;
			}
		}
		
		// 发送SDO响应
		if (HAL_CAN_AddTxMessage(&hcan, &TxMessage1, CAN_Tx_Data, &TxMailbox) != HAL_OK)
		{
			printf("SDO response send failed\n");
		}
		break;
		
	case SDO_CMD_READ:    // 0x40 - 读取请求
	case SDO_R_1:		  // SDO读1字节
	case SDO_R_2:		  // SDO读2字节
	case SDO_R_4:		  // SDO读4字节
		
		// 处理新的对象字典读取
		if(index == OD_BOOT_STATUS && subindex == 0)
		{
			// 返回启动状态
			// uint32_t status = Get_Device_Status();
			// CAN_Tx_Data[0] = SDO_RSP_READ_4BYTE;  // 0x43
			// CAN_Tx_Data[1] = pbuf[1];
			// CAN_Tx_Data[2] = pbuf[2];
			// CAN_Tx_Data[3] = pbuf[3];
			// CAN_Tx_Data[4] = (status >> 24) & 0xFF;  // 大端序
			// CAN_Tx_Data[5] = (status >> 16) & 0xFF;
			// CAN_Tx_Data[6] = (status >> 8) & 0xFF;
			// CAN_Tx_Data[7] = status & 0xFF;
		}

		
		else
		{
			// 原有的读取处理逻辑
			SDO_Access_OBJ.Index = pbuf[1] + (pbuf[2] << 8); // 获取索引（小端序）
			SDO_Access_OBJ.SubIndex = pbuf[3];				  // 获取子索引
			SDO_Access_OBJ.OD_pointer = &pbuf[4];			  // 数据指针
			state = Read_OD(SDO_Access_OBJ);				  // 读取对象字典

			memcpy(&CAN_Tx_Data[1], &pbuf[1], 7); // 复制索引等信息到响应数据中
			if (state == 1)						  // 读取成功
			{
				CAN_Tx_Data[0] = SDO_RSP_WRITE_OK; // 0x60
			}
			else // 读取失败
			{
				CAN_Tx_Data[0] = SDO_RSP_ABORT; // 0x80
			}
		}
		
		// 发送SDO响应
		if (HAL_CAN_AddTxMessage(&hcan, &TxMessage1, CAN_Tx_Data, &TxMailbox) != HAL_OK)
		{
			printf("SDO response send failed\n");
		}
		break;
		
	default:
		// 不支持的命令
		CAN_Tx_Data[0] = SDO_RSP_ABORT;  // 0x80
		CAN_Tx_Data[1] = pbuf[1];
		CAN_Tx_Data[2] = pbuf[2];
		CAN_Tx_Data[3] = pbuf[3];
		CAN_Tx_Data[4] = 0x01;  // 错误码 0x05040001 - 不支持的命令
		CAN_Tx_Data[5] = 0x00;
		CAN_Tx_Data[6] = 0x04;
		CAN_Tx_Data[7] = 0x05;
		
		if (HAL_CAN_AddTxMessage(&hcan, &TxMessage1, CAN_Tx_Data, &TxMailbox) != HAL_OK)
		{
			printf("SDO abort response send failed\n");
		}
		printf("Unsupported SDO command: 0x%02X\n", pbuf[0]);
		break;
	}
	return state;
}

/**
 * @brief 进入bootloader模式函数
 * @note 在RAM高地址写入标志位，然后软复位
 */
void Enter_Bootloader_Mode(void)
{
	// 禁用全局中断
	__disable_irq();
	
	// 这个地址通常在复位时会保持短暂时间
	#define BOOTLOADER_FLAG_ADDRESS    (0x20005000 - 4)  // RAM最高地址-4
	#define BOOTLOADER_FLAG_VALUE      0x20004F00
	
	*(volatile uint32_t *)BOOTLOADER_FLAG_ADDRESS = BOOTLOADER_FLAG_VALUE;
	
	// 确保写入完成
	__DSB();
	__ISB();
	
	// 执行软复位
	NVIC_SystemReset();
}

/**
 * @brief 进入bootloader模式函数（使用固定地址全局变量）
 * @note 使用固定地址全局变量存储标志位，然后软复位
 */
void Enter_Bootloader_Mode_Alt(void)
{
	// 禁用全局中断
	__disable_irq();
	
	// 设置bootloader标志位到固定地址
	bootloader_flag = 0x20004F00;
	
	// 执行软复位
	NVIC_SystemReset();
}


